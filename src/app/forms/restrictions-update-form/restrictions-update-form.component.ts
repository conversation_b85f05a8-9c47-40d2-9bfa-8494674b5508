import {AfterViewInit, Component, inject, Input, OnInit, ViewChild} from '@angular/core';
import {RestrictionsUpdate, RestrictionsUpdateRange, PlanRestrictionsUpdate, Restrictions, PricingRange} from '../../data/pricing';
import {
   UntypedFormArray,
   UntypedFormBuilder,
   UntypedFormGroup,
   Validators
} from '@angular/forms';
import {ID} from '../../data/identifiable';
import {DateTime} from 'luxon';
import {MatDateRangePicker} from '@angular/material/datepicker';
import {PricingService} from 'src/app/services/pricing.service';
import {BundleService} from 'src/app/services/bundle.service';
import {Price} from 'src/app/data/price';
import {Bundle} from '../../data/bundles/bundle';
import {DateRange, DayOfWeek} from '../../data/common';
import {cmpName} from '../../utility/utility';

@Component({
   selector: 'app-restrictions-update-form',
   templateUrl: './restrictions-update-form.component.html',
   styleUrls: ['./restrictions-update-form.component.scss'],
   standalone: false
})
export class RestrictionsUpdateFormComponent implements OnInit, AfterViewInit {
   @ViewChild(MatDateRangePicker) datePicker!: MatDateRangePicker<DateTime>;

   @Input() data?: RestrictionsUpdate;

   form = this.fb.group({
      ranges: this.fb.array([])
   });

   prices: Price[] = [];
   bundles: Bundle[] = [];
   days = DayOfWeek;

   start = this.fb.control(null);
   end = this.fb.control(null);

   private sPricing = inject(PricingService);
   private sBundle = inject(BundleService);

   constructor(private fb: UntypedFormBuilder) {
   }

   get valid(): boolean {
      return this.form.valid;
   }

   get value(): RestrictionsUpdate {
      return {
         ranges: this.ranges.value.map((rangeValue: any) => ({
            range: {
               dateRange: rangeValue.dateRange,
               priority: rangeValue.priority,
               activeWeekDays: rangeValue.enableWeekDays ? rangeValue.activeWeekDays : undefined
            },
            updates: rangeValue.updates.map((update: any) => {
               // Create restrictions object with only included values
               const restrictions: Partial<Restrictions> = {
                  rate: update.restrictions.rate,
                  minStayArrival: update.restrictions.minStayArrival,
                  minStayThrough: update.restrictions.minStayThrough,
                  maxStay: update.restrictions.maxStay
               };

               // Only include boolean values if their toggle is on
               if (update.restrictions.includeClosedToArrival) {
                  restrictions.closedToArrival = update.restrictions.closedToArrival;
               }
               if (update.restrictions.includeClosedToDeparture) {
                  restrictions.closedToDeparture = update.restrictions.closedToDeparture;
               }
               if (update.restrictions.includeStopSell) {
                  restrictions.stopSell = update.restrictions.stopSell;
               }

               return {
                  ratePlanId: update.ratePlanId?.id || update.ratePlanId,
                  restrictions: restrictions as Restrictions
               };
            })
         }))
      };
   }

   get ranges(): UntypedFormArray {
      return this.form.get('ranges') as UntypedFormArray;
   }

   ngOnInit(): void {
      this.sPricing.getAllPrices().subscribe(prices => {
         this.prices = prices.sort(cmpName);
      });

      this.sBundle.getAll().subscribe(bundles => {
         this.bundles = bundles.sort(cmpName);

         if (this.data) {
            this.data.ranges.forEach(r => this.addRange(r));
         }
      });
   }

   ngAfterViewInit(): void {
      this.datePicker.openedStream.subscribe(() => {
         this.start.setValue(null);
         this.end.setValue(null);
      });

      this.datePicker.closedStream.subscribe(() => {
         if (this.start.value && this.end.value) {
            const dateRange = {start: this.start.value, end: this.end.value};
            const pricingRange = {dateRange, priority: 0} as PricingRange;
            const restrictionsUpdateRange: RestrictionsUpdateRange = {
               range: pricingRange,
               updates: []
            };
            this.addRange(restrictionsUpdateRange);
         }
      });
   }

   getDateRange(index: number): DateRange {
      return this.ranges.at(index)?.get('dateRange')?.value as DateRange;
   }

   getUpdatesArray(rangeIndex: number): UntypedFormArray {
      return this.ranges.at(rangeIndex)?.get('updates') as UntypedFormArray;
   }

   addPlanUpdate(rangeIndex: number): void {
      const updatesArray = this.getUpdatesArray(rangeIndex);
      const planUpdateGroup = this.createPlanUpdateGroup();
      updatesArray.push(planUpdateGroup);
   }

   removePlanUpdate(rangeIndex: number, updateIndex: number): void {
      const updatesArray = this.getUpdatesArray(rangeIndex);
      updatesArray.removeAt(updateIndex);
   }

   originalOrder(_: any, __: any) {
      return 0;
   }

   private addRange(range: RestrictionsUpdateRange): void {
      const rangeGroup = this.fb.group({
         dateRange: this.fb.group({
            start: [range.range.dateRange.start, Validators.required],
            end: [range.range.dateRange.end, Validators.required]
         }),
         enableWeekDays: this.fb.control(false),
         activeWeekDays: this.fb.control(range.range.activeWeekDays),
         priority: this.fb.control(range.range.priority),
         updates: this.fb.array([])
      });

      rangeGroup.get('enableWeekDays')?.valueChanges
         .subscribe(newValue => this.setWeekDaysDisabledState(newValue, rangeGroup));
      rangeGroup.patchValue({
         enableWeekDays: range.range.activeWeekDays !== undefined
      });

      // Add existing plan updates
      const updatesArray = rangeGroup.get('updates') as UntypedFormArray;
      range.updates.forEach(update => {
         const planUpdateGroup = this.createPlanUpdateGroup(update);
         updatesArray.push(planUpdateGroup);
      });

      this.ranges.push(rangeGroup);
   }

   private createPlanUpdateGroup(update?: PlanRestrictionsUpdate): UntypedFormGroup {
      // If we have existing data, find the bundle by ID
      let selectedBundle = null;
      if (update?.ratePlanId && this.bundles.length > 0) {
         selectedBundle = this.bundles.find(b => b.id === update.ratePlanId);
      }

      return this.fb.group({
         ratePlanId: [selectedBundle || null, Validators.required],
         restrictions: this.fb.group({
            rate: [update?.restrictions.rate || null],
            minStayArrival: [update?.restrictions.minStayArrival || null],
            minStayThrough: [update?.restrictions.minStayThrough || null],
            maxStay: [update?.restrictions.maxStay || null],
            // Add toggle controls for boolean values
            includeClosedToArrival: [update?.restrictions.closedToArrival !== undefined, Validators.required],
            closedToArrival: [update?.restrictions.closedToArrival || false],
            includeClosedToDeparture: [update?.restrictions.closedToDeparture !== undefined, Validators.required],
            closedToDeparture: [update?.restrictions.closedToDeparture || false],
            includeStopSell: [update?.restrictions.stopSell !== undefined, Validators.required],
            stopSell: [update?.restrictions.stopSell || false]
         })
      });
   }

   private setWeekDaysDisabledState(enabled: boolean, rangeGroup: UntypedFormGroup): void {
      const activeWeekDaysControl = rangeGroup.get('activeWeekDays');
      if (enabled) {
         activeWeekDaysControl?.enable();
      } else {
         activeWeekDaysControl?.disable();
         activeWeekDaysControl?.setValue(undefined);
      }
   }
}
