<form [class.readonly]="disabled" [formGroup]="form">
   <div class="two-column-grid">
      <div>
         <app-customer-select (customerChanged)="handleCustomerChange($event)"
                              [control]="titular"
                              [noAction]="disabled"/>
         <div class="reservation-info-grid">
            <mat-form-field id="reservation-date-inputs">
               <mat-label>Начало</mat-label>
               <mat-date-range-input #reservationDates [rangePicker]="picker">
                  <input formControlName="start" matStartDate placeholder="Начална дата">
                  <input formControlName="end" matEndDate placeholder="Крайна дата">
               </mat-date-range-input>
               <mat-datepicker-toggle [for]="picker" matSuffix></mat-datepicker-toggle>
               <mat-date-range-picker #picker></mat-date-range-picker>
            </mat-form-field>
            <div></div>
            <div class="info-icon">
               <mat-icon inline matTooltip="Брой нощувки">date_range</mat-icon>
               {{value | reservationDuration}}
            </div>
            <div class="info-icon">
               <mat-icon inline matTooltip="Брой гости">people</mat-icon>
               {{value | reservationPeople}}
            </div>
            <div></div>
         </div>
         <mat-form-field>
            <mat-select [compareWith]="equalRooms" formControlName="roomType" placeholder="Тип стая">
               @for (roomType of roomConsumables; track roomType.id) {
                  <mat-option [value]="roomType">{{roomType.name}}</mat-option>
               }
            </mat-select>
         </mat-form-field>
         <mat-form-field>
            <mat-icon matPrefix>meeting_room</mat-icon>
            <mat-label>Стая</mat-label>
            <mat-select [compareWith]="equalRooms" formControlName="room">
               <mat-option [value]="null"></mat-option>
               @for (room of rooms$ | async; track room.id) {
                  <mat-option [class.bold-text]="roomType?.id === room.baseConsumable.id"
                              [value]="room">
                     {{room.name}} ({{room.baseConsumable.name}})
                  </mat-option>
               }
            </mat-select>
         </mat-form-field>
         <app-bundle-select [control]="bundle" [reservation]="reservation$ | async"/>
         <div class="price-grid">
            <app-money-input [group]="manualPrice" label="Ръчна цена" min="0"/>
            <div class="final-price">
               <p>Крайна цена</p>
               <p class="amount">{{price$ | async | money}}</p>
            </div>
         </div>
         <app-customer-select [control]="invoiceReceiver" icon="receipt_long"
                              label="Лице за фактура"/>
         <div>
            <app-reservation-source-input [control]="source"/>
            <app-color-input [control]="color" class="color-button"/>
            @if (allowSourceIdentifier) {
               <mat-form-field>
                  <mat-icon matPrefix>tag</mat-icon>
                  <mat-label>Код от туроператор</mat-label>
                  <input autocomplete="off" formControlName="otaIdentifier" matInput type="text">
               </mat-form-field>
            }
         </div>
         @if (vouchers.length > 0) {
            <app-voucher-input (changeVoucher)="updateVoucher($event)"
                               [voucherDiscount]="voucherDiscount"
                               [vouchers]="vouchers"/>
         }
      </div>
      <div style="margin-top: 70px">
         <app-guest-group-count-input [control]="guestGroupCount"/>
         <app-customer-list-input (countChanged)="updateCount($event)"
                                  (customerChanged)="handleCustomerChange($event)"
                                  [customerArray]="guests" [disabled]="disabled"
                                  [hasMoreSpace]="!roomCapacityFull(value)"/>
      </div>
   </div>
</form>
