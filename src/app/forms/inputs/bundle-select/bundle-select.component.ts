import {
   boolean<PERSON>ttribute,
   Component,
   inject,
   Input,
   OnChanges,
   OnInit,
   SimpleChange,
   SimpleChanges
} from '@angular/core';
import {UntypedFormControl} from '@angular/forms';
import {Observable} from 'rxjs';
import {Bundle} from '../../../data/bundles/bundle';
import {BundleService} from '../../../services/bundle.service';
import {ReservationService} from '../../../services/reservation.service';
import {Reservation} from '../../../data/reservation';
import {map, tap} from 'rxjs/operators';
import {ConsumableType} from '../../../data/bundles/consumable';
import {Offer} from '../../../data/offer';
import {OfferService} from '../../../services/offer.service';
import {validGGC} from '../../../utility/reservation-utility';
import {cmpName, equalIdentifiables} from '../../../utility/utility';
import {ID} from '../../../data/identifiable';

const noRoomConsumables = (b: Bundle) => !b.consumableRules.some(
   ca => ca.consumable.type === ConsumableType.room || ca.consumable.type ===
      ConsumableType.tax || ca.consumable.type === ConsumableType.leisure
);

@Component({
   selector: 'app-bundle-select',
   templateUrl: './bundle-select.component.html',
   standalone: false
})
export class BundleSelectComponent implements OnInit, OnChanges {
   @Input() control!: UntypedFormControl;
   @Input() title = 'Пакет';
   @Input() reservation: Partial<Reservation> | null = null;
   @Input() offer: Partial<Offer> | null = null;
   @Input() consumableFilter?: ID;
   @Input({transform: booleanAttribute}) filterRoomBundles = false;
   @Input({transform: booleanAttribute}) fetchAll = false;
   @Input({transform: booleanAttribute}) wide = false;

   bundles$: Observable<Bundle[]> | undefined;
   eqBundles = equalIdentifiables;

   private sBundle = inject(BundleService);
   private sReservation = inject(ReservationService);
   private sOffer = inject(OfferService);

   ngOnInit(): void {
      this.renewData();
   }

   ngOnChanges(changes: SimpleChanges) {
      const {reservation: r, offer: o, consumableFilter: cf} = changes;
      if ((r && (!r.previousValue || this.needRefreshForReservation(r))) ||
         (o && (!o.previousValue || this.needRefreshForOffer(o))) || cf) {
         this.renewData();
      }
   }

   private renewData(): void {
      let observable: Observable<Bundle[]> | undefined;

      const res = this.reservation;
      const ofr = this.offer;

      if (res && res.start && res.end && res.roomType && res.source &&
         validGGC(res.guestGroupCount)) {
         observable = this.sReservation.getAvailableBundles(res);
      } else if (ofr && ofr.start && ofr.end && ofr.source &&
         validGGC(ofr.guestGroupCount)) {
         observable = this.sOffer.getAvailableBundles(ofr);
      } else if (this.fetchAll) {
         observable = this.sBundle.getAll();
      }

      if (observable) {
         observable = this.filterRoomBundles ?
            observable.pipe(map(bs => bs.filter(noRoomConsumables))) :
            observable.pipe(map(bs => bs.sort(cmpName)));

         if (this.consumableFilter) {
            observable = observable.pipe(map(bs => bs.filter(b => b.consumableRules.some(
               cr => cr.consumable.id === this.consumableFilter))));
         }

         this.bundles$ = observable.pipe(tap(bs => {
            const {value} = this.control;
            if (bs.length === 1) {
               this.control.setValue(bs[0]);
            } else if (value && bs.findIndex(b => b.id === value.id) === -1) {
               this.control.setValue(null);
            }
         }));
      }
   }

   private needRefreshForReservation({
                                        previousValue: previous,
                                        currentValue: current
                                     }: SimpleChange): boolean {
      const {start: s1, end: e1, room: r1, source: ss1, guestGroupCount: ggc1} = previous;
      const {start: s2, end: e2, room: r2, source: ss2, guestGroupCount: ggc2} = current;

      return (s2 && !s1?.equals(s2))
         || (e2 && !e1?.equals(e2))
         || r1?.id !== r2?.id
         || ss1?.id !== ss2?.id
         || JSON.stringify(ggc1) !== JSON.stringify(ggc2);
   }

   private needRefreshForOffer({
                                  previousValue: previous,
                                  currentValue: current
                               }: SimpleChange): boolean {
      const {start: s1, end: e1, source: ss1, guestGroupCount: ggc1} = previous;
      const {start: s2, end: e2, source: ss2, guestGroupCount: ggc2} = current;

      return (s2 && !s1?.equals(s2))
         || (e2 && !e1?.equals(e2))
         || ss1.id !== ss2.id
         || JSON.stringify(ggc1) !== JSON.stringify(ggc2);
   }
}
