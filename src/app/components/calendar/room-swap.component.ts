import {Component, inject, Input, OnChanges, SimpleChanges} from '@angular/core';
import {ReservationInfo} from '../../data/reservation';
import {Room} from '../../data/room';
import {CdkDragDrop, moveItemInArray, transferArrayItem} from '@angular/cdk/drag-drop';
import {ID} from '../../data/identifiable';
import {RoomSwapData} from './calendar.component';
import {cmpName} from '../../utility/utility';
import {ReservationService} from '../../services/reservation.service';

@Component({
   selector: 'app-room-swap',
   template: `
      <div cdkDropListGroup>
         @for (r of rooms | keyvalue; track r.key.id) {
            <div cdkDropList [cdkDropListData]="r.value"
                 (cdkDropListDropped)="drop($event)" class="room">
               <h2 [class.warn-text]="isBad(r.key.id)">
                  Стая {{r.key.name}}
               </h2>
               @for (res of r.value; track res.id) {
                  <div cdkDrag class="flex-row" style="align-items: center;">
                     <mat-icon cdkDragHandle>open_with</mat-icon>
                     <span [class.warn-text]="isBad(res.id)">
                                 {{res | reservation}}
                           </span>
                  </div>
               }
            </div>
         }
      </div>
   `,
   styles: [`
      .room {
         padding: 4px;
      }

      .cdk-drag-placeholder {
         opacity: 0;
      }

      .cdk-drag-animating {
         transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
      }

      .cdk-drop-list-dragging .room:not(.cdk-drag-placeholder) {
         transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
      }
   `],
   standalone: false
})
export class RoomSwapComponent implements OnChanges {
   @Input({required: true}) data?: RoomSwapData;

   rooms: Map<Room, ReservationInfo[]> = new Map();
   clean = true;

   private conflicts: ID[] = [''];

   private sReservation = inject(ReservationService);

   get value(): Record<ID, ID> {
      const result: Record<ID, ID> = {};

      this.rooms.forEach((reservations, newRoom) => {
         reservations.forEach(reservation => result[reservation.id] = newRoom.id);
      });

      return result;
   }

   get hasConflicts(): boolean {
      return this.conflicts.length !== 0;
   }

   ngOnChanges(changes: SimpleChanges): void {
      if (changes.data && changes.data.currentValue) {
         const data: RoomSwapData = changes.data.currentValue;

         this.clean = true;
         this.rooms.clear();
         this.conflicts = [''];

         [
            ...data.reservations.map(r => r.room),
            ...data.rooms.filter(
               r => !data.reservations.some(res => res.room!!.id === r.id))
         ].sort(cmpName).forEach(room => this.rooms.set(room!!, []));

         for (const reservation of data.reservations) {
            this.rooms.get(reservation.room!!)!.push(reservation);
         }
      }
   }

   drop(event: CdkDragDrop<ReservationInfo[]>) {
      if (event.previousContainer === event.container) {
         moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
      } else {
         transferArrayItem(
            event.previousContainer.data,
            event.container.data,
            event.previousIndex,
            event.currentIndex,
         );

         this.clean = false;
         this.sReservation.validateRoomSwap(this.value)
            .subscribe(cs => this.conflicts = cs);
      }
   }

   isBad(id: ID): boolean {
      return this.conflicts.findIndex(badId => badId === id) !== -1;
   }
}
