import {
   AfterViewInit,
   ChangeDetectorRef,
   Component,
   computed,
   EmbeddedViewRef,
   EventEmitter,
   HostListener,
   inject,
   Injectable,
   OnDestroy,
   Output,
   QueryList,
   signal,
   TemplateRef,
   ViewChild,
   ViewChildren
} from '@angular/core';
import {DateTime} from 'luxon';
import {
   canonicalDate,
   cmpName,
   cumulativeOffsetTop,
   getDuration,
   isRangeInView,
   last,
   today
} from '../../utility/utility';
import {RoomService} from '../../services/room.service';
import {Room} from '../../data/room';
import {ReservationService} from '../../services/reservation.service';
import {Reservation, ReservationInfo, ReservationStatus} from '../../data/reservation';
import {
   ReservationContainerDirective
} from '../../directives/reservation-container.directive';
import {
   ReservationDialogComponent,
   ReservationDialogData
} from '../../dialogs/reservation-dialog/reservation-dialog.component';
import {filter, map, mergeMap, switchMap} from 'rxjs/operators';
import {MatDialog} from '@angular/material/dialog';
import {ViewContainerRefDirective} from '../../directives/view-container-ref.directive';
import {
   CheckInDialogComponent
} from '../../dialogs/check-in-dialog/check-in-dialog.component';
import {
   CheckOutDialogComponent
} from '../../dialogs/check-out-dialog/check-out-dialog.component';
import {forkJoin, zip} from 'rxjs';
import {CustomerService} from '../../services/customer.service';
import {
   BalanceDialogComponent,
   BalanceDialogData,
   FinancialContext
} from '../../dialogs/balance-dialog/balance-dialog.component';
import {
   DateRange,
   MAT_DATE_RANGE_SELECTION_STRATEGY,
   MatDateRangeInput,
   MatDateRangeSelectionStrategy
} from '@angular/material/datepicker';
import {DateAdapter} from '@angular/material/core';
import {COLORS} from '../../forms/inputs/color-input/colors';
import {getDays, getResStatusName} from '../../utility/reservation-utility';
import {NotificationService} from '../../services/notification.service';
import {
   GroupReservationDialogComponent,
   GroupReservationDialogData
} from '../../dialogs/group-reservation-dialog/group-reservation-dialog.component';
import {fullScreenDialogOptions} from '../../utility/dialog-utility';
import {
   ReservationSelectDialogComponent,
   ReservationSelectDialogData
} from '../../dialogs/reservation-select-dialog/reservation-select-dialog.component';
import {ID} from '../../data/identifiable';
import {
   ReservationHistoryDialogComponent,
   ReservationHistoryDialogInput
} from '../../dialogs/reservation-history-dialog/reservation-history-dialog.component';
import {fade} from '../../animations/fade';
import {SelectionModel} from '@angular/cdk/collections';
import {DateRange2} from '../../data/common';
import {NotesService} from '../../services/notes.service';
import {Note} from '../../data/notes';
import {LoadingService} from '../../navigation/loading.service';
import {LicenseService} from '../../services/license.service';
import {FinancialAccountService} from 'src/app/services/financial-account.service';
import {ReservationPipe} from 'src/app/pipes/reservation.pipe';
import {Cancellation} from 'src/app/data/cancellation';
import {fullName} from 'src/app/data/customers/customer';
import {CancellationService} from 'src/app/services/cancellation.service';

export interface RoomSwapData {
   rooms: Room[];
   reservations: ReservationInfo[];
}

let resolution = 7;

@Injectable()
export class RangeSelectionStrategy<D> implements MatDateRangeSelectionStrategy<D> {
   constructor(private dateAdapter: DateAdapter<D>) {
   }

   selectionFinished(date: D | null): DateRange<D> {
      return this.createRange(date);
   }

   createPreview(activeDate: D | null): DateRange<D> {
      return this.createRange(activeDate);
   }

   private createRange(date: D | null): DateRange<D> {
      if (date) {
         const start = date;
         const end = this.dateAdapter.addCalendarDays(date, resolution - 1);
         return new DateRange<D>(start, end);
      }

      return new DateRange<D>(null, null);
   }
}

@Component({
   selector: 'app-calendar',
   templateUrl: './calendar.component.html',
   styleUrls: ['./calendar.component.scss'],
   providers: [
      {
         provide: MAT_DATE_RANGE_SELECTION_STRATEGY,
         useClass: RangeSelectionStrategy,
      },
   ],
   animations: [fade(500)],
   standalone: false
})
export class CalendarComponent implements AfterViewInit, OnDestroy {
   @ViewChildren(ReservationContainerDirective)
   reservationContainers!: QueryList<ReservationContainerDirective>;
   @ViewChild(ViewContainerRefDirective) briefContainer!: ViewContainerRefDirective;
   @ViewChild('reservationCard') reservationCardTemplate!: TemplateRef<any>;
   @ViewChild('roomBlock') roomBlockTemplate!: TemplateRef<any>;
   @ViewChild('reservationPreview') reservationPreviewTemplate!: TemplateRef<any>;
   @ViewChild('reservationBrief') reservationBriefTemplate!: TemplateRef<any>;
   @ViewChild(MatDateRangeInput) rangeInput!: MatDateRangeInput<DateTime>;

   @Output() swapRooms = new EventEmitter<RoomSwapData>();

   days = signal(getDays(resolution));
   dateStart = computed(() => this.days()[0]);
   dateEnd = computed(() => last(this.days()));
   extended = false;
   creatingGroup = false;
   swappingRooms = false;
   reservationSelection = new SelectionModel<ReservationInfo>(true, []);
   roomSelection = new SelectionModel<Room>(true, []);
   groupDays: Map<Room, [DateTime, DateTime]> = new Map();

   rooms: Room[] = [];
   roomCapacities: string[] = [];

   rs = ReservationStatus;
   getResStatusName = getResStatusName;

   checkingIn: ReservationInfo[] = [];
   checkingOut: ReservationInfo[] = [];

   protected sLicense = inject(LicenseService);

   private checkInIndex = 0;
   private checkOutIndex = 0;

   private reservationsMap: Map<ID, ReservationInfo[]> = new Map();

   private dragRoom: Room | null = null;
   private dragStartDay: DateTime | null = null;
   private dragFirstStartDay: DateTime | null = null;
   private dragFirstEndDay: DateTime | null = null;
   private resPreviews: EmbeddedViewRef<any>[] = [];
   private resPreviewDayClass = '';

   private today = today();

   private sRoom = inject(RoomService);
   private sReservation = inject(ReservationService);
   private sCustomer = inject(CustomerService);
   private sNotification = inject(NotificationService);
   private sNotes = inject(NotesService);
   private sLoading = inject(LoadingService);
   private sFinancialAccount = inject(FinancialAccountService);
   private sCancellation = inject(CancellationService);
   private dialog = inject(MatDialog);
   private cd = inject(ChangeDetectorRef);
   private cleaningNotes: Record<ID, Note[]> = {};

   get activeAddons(): boolean {
      return this.creatingGroup || this.swappingRooms;
   }

   @HostListener('body:dragover', ['$event'])
   ondragover(event: MouseEvent) {
      event.preventDefault();
      this.resizeLatestReservationPreview(event);
   }

   @HostListener('body:drop', ['$event'])
   ondrop(event: DragEvent) {
      event.preventDefault();

      const dropDay = this.getDayUnderMouse(event);
      this.resizeLatestReservationPreview(event, dropDay);

      if (this.dragStartDay && this.dragRoom) {
         let startDay: DateTime;
         let endDay: DateTime;

         if (this.dragStartDay < dropDay) {
            startDay = this.dragStartDay.plus({seconds: 1});
            endDay = dropDay.plus({days: 1});
         } else {
            startDay = dropDay.plus({seconds: 1});
            endDay = this.dragStartDay.plus({days: 1});
         }

         if (this.creatingGroup) {
            this.groupDays.set(this.dragRoom, [startDay, endDay]);

            if (!this.dragFirstStartDay) {
               this.dragFirstStartDay = startDay;
               this.dragFirstEndDay = endDay;
            }
         } else {
            this.addReservation(startDay, endDay, this.dragRoom);
         }
      }
   }

   @HostListener('body:click')
   @HostListener('document:keydown.escape')
   closeBrief(): void {
      this.briefContainer.viewContainerRef.clear();
   }

   @HostListener('body:keydown.escape')
   clearGroupSelection(): void {
      this.creatingGroup = false;
      this.dragFirstStartDay = null;
      this.dragFirstEndDay = null;

      this.resPreviews.forEach(rp => rp.destroy());
      this.resPreviews = [];
      this.groupDays.clear();
   }

   @HostListener('body:keydown.escape')
   clearRoomSwap(): void {
      this.swappingRooms = false;
      this.reservationSelection.clear();
      this.roomSelection.clear();
   }

   ondragstart(event: DragEvent, room: Room, day: DateTime): boolean {
      this.cd.detach();
      event.dataTransfer?.setDragImage(new Image(), 0, 0);

      if (this.isCalendarOccupied(day.plus({seconds: 1}), day.plus({days: 1}), room)) {
         return false;
      }
      if (this.swappingRooms) {
         return false;
      }

      this.creatingGroup = this.creatingGroup || event.ctrlKey || event.altKey;

      if (this.creatingGroup && this.groupDays.has(room)) {
         return false;
      }

      this.dragRoom = room;
      this.dragStartDay = day;

      setTimeout(() => this.createDragPreview(room, day), 0);
      return true;
   }

   ondragend(): void {
      if (this.creatingGroup && this.dragRoom && !this.groupDays.has(this.dragRoom)) {
         this.resPreviews.pop()?.destroy();
         this.pruneGroupSelection();
      }

      this.dragRoom = null;
      this.dragStartDay = null;

      if (!this.creatingGroup) {
         this.resPreviews.forEach(rp => rp.destroy());
         this.resPreviews = [];
      }

      this.cd.reattach();
   }

   ngAfterViewInit(): void {
      this.renewRooms();
      this.renewReservationsInView();
      this.cd.detectChanges();

      zip(this.rangeInput._startInput.dateChange, this.rangeInput._endInput.dateChange)
         .subscribe(([{value: dateStart}, {value: dateEnd}]) =>
            dateStart && dateEnd && this.renewDates(dateStart, dateEnd));
   }

   ngOnDestroy(): void {
      resolution = 7;
   }

   renewReservationsInView(): void {
      this.reservationContainers.forEach(rc => rc.viewContainerRef.clear());

      const days = this.days();
      const start = days[0];
      const end = last(days).plus({seconds: 1});

      this.sCustomer.loadGuestsInRange(start, end).pipe(
         switchMap(() => forkJoin({
            reservations: this.sReservation.getInRange(start, end),
            todayReservations: this.sReservation.getInRange(this.today,
               this.today.plus({seconds: 1})),
            purchases: this.sReservation.getTotalPurchasesInRange(start, end),
            payments: this.sReservation.getTotalPaymentsInRange(start, end),
            notes: this.sReservation.getNotesInRange(start, end),
            scores: this.sReservation.getAvgScoresInRange(start, end),
         }))
      ).subscribe(
         ({reservations, todayReservations, purchases, payments, notes, scores}) =>
            this.displayReservations(
               reservations, todayReservations, purchases, payments, notes, scores, true)
      );
   }

   addReservation(start: DateTime, end: DateTime, room?: Room): void {
      if (this.isCalendarOccupied(start, end, room)) {
         return;
      }

      const data: ReservationDialogData = {
         data: {room, start, end},
         edit: false
      };

      this.dialog.open(ReservationDialogComponent, {
         data,
         ...fullScreenDialogOptions,
      }).afterClosed().pipe(
         filter(result => !!result),
         switchMap(result => this.sReservation.add(result.addReservation).pipe(
            map(reservation => ({reservation, draftId: result.draftId}))
         )),
         mergeMap(({reservation, draftId}) => this.sNotes.moveToNewParent(draftId,
            reservation.id).pipe(map(() => draftId))),
         mergeMap(draftId => this.sReservation.deleteDraft(draftId))
      ).subscribe(() => {
         this.renewReservationsInView();
      });
   }

   addGroupReservation(): void {
      const data: GroupReservationDialogData = {data: this.groupDays};

      this.dialog.open(GroupReservationDialogComponent, {
         data,
         ...fullScreenDialogOptions,
      }).afterClosed().pipe(
         filter(result => !!result),
         switchMap(result => this.sReservation.createGroup(result)),
      ).subscribe({
         next: () => this.renewReservationsInView(),
         error: () => this.clearGroupSelection(),
         complete: () => this.clearGroupSelection()
      });
   }

   viewReservation(res: Reservation, readonly = false): void {
      this.closeBrief();

      const data: ReservationDialogData = {
         data: res,
         edit: true,
         readonly,
         notesCallback: () => this.renewReservationsInView()
      };

      this.dialog.open(ReservationDialogComponent, {
         data,
         ...fullScreenDialogOptions,
      }).afterClosed().pipe(
         filter(result => !!result),
         switchMap(result => {
            if ('addReservation' in result) {
               return this.sReservation.update(result.addReservation);
            } else {
               return this.sReservation.delete(result.deleteReservation);
            }
         })
      ).subscribe(() => {
         this.renewReservationsInView();
         const invalidateCache = !readonly;
         this.renewRooms(invalidateCache);
      });
      // TODO(vlado): renew in view on error (only when updating a reservation)
   }

   deleteReservation(reservation: Reservation): void {
      this.closeBrief();

      this.sNotification.openConfirmationDialog({
         title: 'Изтриване на резервация',
         description: `Наистина ли искате да изтриете резервацията на
                     ${reservation.titular.contact.name} в стая ${reservation.room.name}?`
      }).pipe(switchMap(() => this.sReservation.delete(reservation.id)))
         .subscribe(() => this.renewReservationsInView());
   }

   cancelReservation(reservation: Reservation): void {
      this.closeBrief();

      this.sNotification.openConfirmationDialog({
         title: 'Анулиране на резервация',
         description: `Наистина ли искате да анулирате резервацията на
                          ${reservation.titular.contact.name} в стая ${reservation.room.name}?`
      }).pipe(
         switchMap(() => this.sReservation.cancel(reservation.id)),
         switchMap(cancellationId => this.sCancellation.get(cancellationId)),
         map(cancellation => this.viewBalance(cancellation, true))
      ).subscribe();
   }

   viewBalance(object: Reservation | Cancellation, isCancelllation: boolean): void {
      this.sFinancialAccount.getChain(object.id).pipe(
         switchMap(accountChain => {
            const data: BalanceDialogData = {
               accountChain,
               movementAllowed: true,
               context: isCancelllation ? FinancialContext.cancellation :
                  FinancialContext.reservation,
               subtitle: isCancelllation ?
                  `Анулирана резервация на ${fullName(object.titular)}` :
                  ReservationPipe.toString((object as Reservation))
            };

            return this.dialog.open(BalanceDialogComponent, {
               data,
               ...fullScreenDialogOptions,
            }).afterClosed();
         })
      ).subscribe(() => this.renewReservationsInView());
   }

   viewHistory(reservation: Reservation): void {
      const data: ReservationHistoryDialogInput = {reservation};
      this.dialog.open(ReservationHistoryDialogComponent, {
         data,
         maxWidth: '95vw',
         width: '95vw',
      });
   }

   openBrief(event: MouseEvent, reservationInfo: ReservationInfo): void {
      event.stopPropagation();

      if (this.swappingRooms) {
         if (reservationInfo.status === ReservationStatus.pending) {
            this.reservationSelection.toggle(reservationInfo);
         }
         return;
      }

      this.closeBrief();
      this.sReservation.get(reservationInfo.id).subscribe(reservation => {
         const brief = this.briefContainer.viewContainerRef.createEmbeddedView(
            this.reservationBriefTemplate, {reservation});

         this.positionBrief(event, brief.rootNodes[0]);
      });
   }

   checkIn(reservation: Reservation): void {
      this.dialog.open(CheckInDialogComponent, {data: reservation}).afterClosed()
         .pipe(
            filter(result => !!result),
            switchMap(guests => this.sReservation.patch({
               id: reservation.id,
               guests,
               status: ReservationStatus.ongoing,
            }))
         ).subscribe(() => this.renewReservationsInView());
   }

   checkOut(reservation: Reservation): void {
      this.dialog.open(CheckOutDialogComponent, {data: reservation}).afterClosed().pipe(
         filter(result => !!result),
         switchMap(() => this.sReservation.patch({
            id: reservation.id,
            status: ReservationStatus.completed
         }))
      ).subscribe(() => {
         if (reservation.isLeisure) {
            this.sRoom.invalidateCache();
            this.renewRooms();
         }
         this.renewReservationsInView();
      });
   }

   nextCheckIn() {
      this.checkInIndex = ++this.checkInIndex % this.checkingIn.length;
      this.focusReservation(this.checkingIn[this.checkInIndex]);
   }

   nextCheckOut() {
      this.checkOutIndex = ++this.checkOutIndex % this.checkingOut.length;
      this.focusReservation(this.checkingOut[this.checkOutIndex]);
   }

   removeFromGroup(viewRef: EmbeddedViewRef<any>): void {
      this.groupDays.delete(viewRef.context.room);
      viewRef.destroy();

      const index = this.resPreviews.indexOf(viewRef);
      this.resPreviews.splice(index, 1);

      this.pruneGroupSelection();
   }

   duplicateFirstInGroup(event: MouseEvent, room: Room): void {
      if (!this.creatingGroup || this.groupDays.has(room) ||
         (!event.ctrlKey && !event.altKey)) {
         return;
      }

      setTimeout(() => {
         const start = this.dragFirstStartDay;
         const end = this.dragFirstEndDay;
         if (start && end) {
            if (this.isCalendarOccupied(start, end, room)) {
               return;
            }
            this.groupDays.set(room, [start, end]);

            const duration = getDuration(start, end);
            this.createDragPreview(room, start, duration);
         }
      }, 0);
   }

   shiftDates(days: number) {
      this.clearAddons();

      this.days.update(ds => ds.map(d => d.plus({days})));
      this.renewReservationsInView();
   }

   setDefaultDates(): void {
      this.clearAddons();

      this.days.set(getDays(resolution));
      this.renewReservationsInView();
   }

   setExtendedDays(): void {
      this.clearAddons();
      this.extended = true;

      resolution = 14;
      this.days.update(days => getDays(resolution, days[0]));

      this.renewReservationsInView();
   }

   setNormalDays(): void {
      this.clearAddons();
      this.extended = false;

      resolution = 7;
      this.days.update(days => getDays(resolution, days[0]));

      this.renewReservationsInView();
   }

   isToday(day: DateTime): boolean {
      return day.year === this.today.year &&
         day.month === this.today.month &&
         day.day === this.today.day;
   }

   disableCheckIn(day: DateTime): boolean {
      const now = DateTime.local();
      const diff = now.diff(day).as('days');

      return diff < -0.5 || diff > 1;
   }

   searchReservation(): void {
      const data: ReservationSelectDialogData = {
         title: 'Търсене на резервация',
         inputLabel: 'Резервация',
         confirmText: 'Покажи резервацията',
         includeFinancialAccounts: false,
      };
      this.dialog.open(ReservationSelectDialogComponent, {data}).afterClosed()
         .pipe(filter(result => !!result))
         .subscribe(reservation => this.focusReservation(reservation));
   }

   startRoomSwap(): void {
      this.closeBrief();
      this.clearAddons();
      this.swappingRooms = true;
   }

   swapSelectedRooms(): void {
      if (this.reservationSelection.selected.length) {
         this.swapRooms.emit({
            rooms: this.roomSelection.selected,
            reservations: this.reservationSelection.selected,
         });
      }
      this.clearRoomSwap();
   }

   startGroupReservation(): void {
      this.clearAddons();
      this.creatingGroup = true;
   }

   clearAddons(): void {
      this.clearGroupSelection();
      this.clearRoomSwap();
   }

   getCleaningNotes(roomId: ID): Note[] | undefined {
      return this.cleaningNotes[roomId];
   }

   inviteToGuestApp(reservation: Reservation): void {
      this.sLoading.start();
      this.sReservation.inviteToGuestApp(reservation.id).subscribe({
         next: () => {
            this.sNotification.displayNotification(
               'Поканата е изпратена успешно!',
               'Добре');
            this.sLoading.stop();
         },
         error: () => this.sLoading.stop()
      });
   }

   private canCheckIn(reservation: ReservationInfo): boolean {
      return reservation.status === ReservationStatus.pending &&
         !this.disableCheckIn(reservation.start);
   }

   private canCheckOut(reservation: ReservationInfo): boolean {
      return reservation.status === ReservationStatus.ongoing &&
         this.isToday(reservation.end);
   }

   private renewDates(start: DateTime, end: DateTime): void {
      start = canonicalDate(start);
      end = canonicalDate(end);

      const days = [];
      for (; start <= end; start = start.plus({day: 1})) {
         days.push(start);
      }
      this.days.set(days);

      this.renewReservationsInView();
   }

   private isCalendarOccupied(start: DateTime, end: DateTime, room?: Room): boolean {
      if (!room) {
         return false;
      }

      if (room.blocked && isRangeInView(room.blocked, start, end)) {
         return true;
      }

      const roomReservations = this.reservationsMap.get(room.id);
      return !!roomReservations?.some(res => isRangeInView(res, start, end));
   }

   private getDayUnderMouse(event: MouseEvent): DateTime {
      const days = this.days();
      const dayIndex = Math.floor(
         event.pageX / document.body.clientWidth * (resolution + 1)) - 1;

      return dayIndex !== -1 ? days[dayIndex] : days[0];
   }

   private createDragPreview(room: Room, day: DateTime, duration: number = 1): void {
      const rc = this.reservationContainers.find(item => item.roomId === room.id);
      if (rc) {
         const resPreview =
            rc.viewContainerRef.createEmbeddedView(this.reservationPreviewTemplate, {
               isGroup: this.creatingGroup,
               room,
            });
         resPreview.context.viewRef = resPreview;

         this.resPreviews.push(resPreview);
         this.resPreviewDayClass = `d${duration}-${resolution}`;

         const position = getDuration(this.days()[0], day) + 1;
         const element = resPreview.rootNodes[0];
         element.classList.add(this.resPreviewDayClass, `p${position}-${resolution}`);
      }
   }

   private positionBrief(event: MouseEvent, brief: HTMLElement) {
      let reservationCard = event.target as HTMLElement;
      while (!reservationCard.classList.contains('reservation-card')) {
         reservationCard = reservationCard.parentElement as HTMLElement;
      }

      const offsetTop = cumulativeOffsetTop(reservationCard);
      const onTop = event.screenY / document.documentElement.clientHeight > 0.73;
      const top = offsetTop + reservationCard.clientHeight + 14 -
         (onTop ? (reservationCard.scrollHeight + brief.scrollHeight + 28) : 0);

      const left = Math.min(event.clientX - 131,
         document.documentElement.clientWidth - brief.clientWidth - 24);

      brief.style.top = `${top}px`;
      brief.style.left = `${left}px`;
   }

   private getReservationStyles(res: ReservationInfo, score?: string): string[] {
      const result: string[] = res.color ?
         [...(COLORS.get(res.color)?.background ?? [res.status])] : [res.status];

      const positionStyles = this.getPositionStyles(res);
      if (res.isLeisure) {
         positionStyles[0] = `d-l-${resolution}`;
      }
      result.push(...positionStyles);

      if (score) {
         result.push(score);
      }

      return result;
   }

   private getPositionStyles({start, end}: DateRange2): [string, string] {
      const days = this.days();
      let duration: number;
      let position: number;
      if (start < days[0]) {
         duration = Math.min(resolution, getDuration(days[0], end));
         position = 1;
      } else if (end > last(days)) {
         duration = getDuration(start, last(days).plus({days: 1}));
         position = getDuration(days[0], start) + 1;
      } else {
         duration = getDuration(start, end);
         position = getDuration(days[0], start) + 1;
      }

      return [`d${duration}-${resolution}`, `p${position}-${resolution}`];
   }

   private resizeLatestReservationPreview(event: MouseEvent, lastDay?: DateTime): void {
      lastDay = lastDay ?? this.getDayUnderMouse(event);
      const resPreview = last(this.resPreviews);

      if (resPreview && this.dragStartDay) {
         const element = resPreview.rootNodes[0];
         const duration = Math.round(lastDay.diff(this.dragStartDay).as('days')) + 1;

         element.classList.remove(this.resPreviewDayClass);

         this.resPreviewDayClass = `d${duration}-${resolution}`;
         element.classList.add(this.resPreviewDayClass);
      }
   }

   private pruneGroupSelection(): void {
      if (this.resPreviews.length === 0) {
         this.clearGroupSelection();
      }
   }

   private focusReservation(info: ReservationInfo): void {
      this.days.set(getDays(resolution, canonicalDate(info.start)));

      this.renewReservationsInView();

      const element = document.getElementById(info.room.id);
      if (element) {
         element.scrollIntoView({behavior: 'smooth'});

         element.classList.toggle('blink');
         setTimeout(() => element.classList.toggle('blink'), 3000);
      }
   }

   private renewRooms(invalidateCache = false): void {
      if (invalidateCache) {
         this.sRoom.invalidateCache();
      }
      this.sRoom.getAll().subscribe(rs => {
         this.rooms = rs.sort(cmpName);
         this.roomCapacities = rs.map(r => `${r.baseCapacity}+${r.additionalCapacity}`);
      });

      this.sRoom.getCleaningNotes().subscribe(notes => this.cleaningNotes = notes);
   }

   private displayReservations(reservations: ReservationInfo[],
      todayReservations: ReservationInfo[],
      purchases: Record<ID, number>,
      payments: Record<ID, number>,
      notes: Record<ID, Note[]>,
      scores: Record<ID, number>,
      immediate: boolean) {
      const sortRes = (lhs: ReservationInfo, rhs: ReservationInfo) =>
         lhs.room.name.localeCompare(rhs.room.name);
      this.checkingIn = todayReservations.filter(r => this.canCheckIn(r)).sort(sortRes);
      this.checkingOut = todayReservations.filter(r => this.canCheckOut(r)).sort(sortRes);

      this.reservationsMap.clear();

      for (const res of reservations) {
         if (this.reservationsMap.has(res.room.id)) {
            // @ts-expect-error we already verified that the map contains res.room.id
            this.reservationsMap.get(res.room.id).push(res);
         } else {
            this.reservationsMap.set(res.room.id, [res]);
         }
      }

      if (immediate) {
         this.displayReservationCards(purchases, payments, notes, scores);
      } else {
         setTimeout(
            () => this.displayReservationCards(purchases, payments, notes, scores), 0);
      }
   }

   private displayReservationCards(purchases: Record<ID, number>,
      payments: Record<ID, number>,
      notes: Record<ID, Note[]>,
      scores: Record<ID, number>): void {
      const days = this.days();
      const start = days[0].plus({seconds: 1});
      const end = last(days).plus({days: 1});

      for (const rc of this.reservationContainers) {
         this.sRoom.get(rc.roomId).subscribe(room => {
            if (room.blocked && isRangeInView(room.blocked, start, end)) {
               rc.viewContainerRef.createEmbeddedView(this.roomBlockTemplate, {
                  room,
                  styles: this.getPositionStyles(room.blocked)
               });
            }
         });

         if (this.reservationsMap.has(rc.roomId)) {
            const reservations = this.reservationsMap.get(rc.roomId) as ReservationInfo[];
            for (const reservation of reservations) {
               if (isRangeInView(reservation, start, end)) {
                  const score = reservation.id in scores ?
                     `score-${Math.round(scores[reservation.id])}` : undefined;

                  rc.viewContainerRef.createEmbeddedView(this.reservationCardTemplate, {
                     reservation,
                     purchases: purchases[reservation.id],
                     payments: payments[reservation.id],
                     notes: notes[reservation.id],
                     styles: this.getReservationStyles(reservation, score),
                  });
               }
            }
         }
      }
   }
}
