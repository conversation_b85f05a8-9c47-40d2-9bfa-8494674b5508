import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';

import {CustomersComponent} from './customers/customers.component';
import {NotFoundComponent} from './not-found/not-found.component';
import {QueriesComponent} from './query/queries/queries.component';
import {ReservationsComponent} from './reservations/reservations.component';
import {AuthGuard} from './auth/auth.guard';
import {LogoutComponent} from './auth/logout.component';
import {OffersComponent} from './offer/offers.component';
import {SettingsComponent} from './settings/settings.component';
import {
   PricingSettingsComponent
} from './settings/individual-settings/pricing-settings/pricing-settings.component';
import {
   BundlesSettingsComponent
} from './settings/individual-settings/bundles-settings/bundles-settings.component';
import {
   RoomsSettingsComponent
} from './settings/individual-settings/rooms-settings/rooms-settings.component';
import {
   DiscountsSettingsComponent
} from './settings/individual-settings/discounts-settings/discounts-settings.component';
import {HomeComponent} from './home/<USER>';
import {
   BusinessSettingsComponent
} from './settings/individual-settings/business-settings/business-settings.component';
import {CleaningComponent} from './cleaning/cleaning.component';
import {
   OperatorsSettingsComponent
} from './settings/individual-settings/operators-settings/operators-settings.component';
import {
   CleaningSettingsComponent
} from './settings/individual-settings/cleaning-settings/cleaning-settings.component';
import {
   SourcesSettingsComponent
} from './settings/individual-settings/sources-settings/sources-settings.component';
import {
   FiscalSettingsComponent
} from './settings/individual-settings/fiscal-settings/fiscal-settings.component';
import {
   ChannelManagerSettingsComponent
} from './settings/individual-settings/channel-manager-settings/channel-manager-settings.component';
import {CancellationsComponent} from './cancellations/cancellations.component';
import {InvoicesComponent} from './invoices/invoices.component';

const routes: Routes = [
   {path: '', component: HomeComponent, pathMatch: 'full'},

   // authenticated routes
   {
      path: 'customer',
      component: CustomersComponent,
      canActivate: [AuthGuard],
      title: 'Клиенти | Overview'
   },
   {
      path: 'query',
      component: QueriesComponent,
      canActivate: [AuthGuard],
      title: 'Справки | Overview'
   },
   {
      path: 'reservation',
      component: ReservationsComponent,
      canActivate: [AuthGuard],
      title: 'Резервации | Overview'
   },
   {
      path: 'cancellations',
      component: CancellationsComponent,
      canActivate: [AuthGuard],
      title: 'Анлуации | Overview'
   },
   {
      path: 'leads',
      component: OffersComponent,
      canActivate: [AuthGuard],
      title: 'Разговори | Overview'
   },
   {
      path: 'cleaning',
      component: CleaningComponent,
      canActivate: [AuthGuard],
      title: 'Почистване | Overview'
   },
   {
      path: 'invoices',
      component: InvoicesComponent,
      canActivate: [AuthGuard],
      title: 'Фактури | Overview'
   },
   {
      path: 'settings',
      component: SettingsComponent,
      canActivate: [AuthGuard],
      children: [
         {path: 'rooms', component: RoomsSettingsComponent},
         {path: 'pricing', component: PricingSettingsComponent},
         {path: 'bundles', component: BundlesSettingsComponent},
         {path: 'discounts', component: DiscountsSettingsComponent},
         {path: 'cleaning', component: CleaningSettingsComponent},
         {path: 'sources', component: SourcesSettingsComponent},
         {path: 'operators', component: OperatorsSettingsComponent},
         {path: 'fiscal', component: FiscalSettingsComponent},
         {path: 'channel-manager', component: ChannelManagerSettingsComponent},
         {path: 'business', component: BusinessSettingsComponent},
      ],
      title: 'Настройки | Overview'
   },
   {path: 'logout', component: LogoutComponent, canActivate: [AuthGuard]},

   // unauthenticated routes
   {path: 'not-found', component: NotFoundComponent, title: '404 | Overview'},

   {path: '**', redirectTo: 'not-found'},
];

@NgModule({
   imports: [RouterModule.forRoot(routes)],
   exports: [RouterModule]
})
export class AppRoutingModule {
}
