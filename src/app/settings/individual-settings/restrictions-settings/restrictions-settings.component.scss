.settings-header {
   display: flex;
   justify-content: space-between;
   align-items: flex-start;
   margin-bottom: 20px;
   gap: 20px;

   .description {
      flex: 1;

      h1 {
         margin: 0 0 10px 0;
         color: #333;
      }

      h3 {
         margin: 0;
         font-weight: 400;
         color: #666;
      }
   }

   button {
      flex-shrink: 0;
   }
}

.restrictions-content {
   margin-top: 20px;

   mat-card {
      margin-bottom: 20px;
   }
}

.license-required {
   display: flex;
   justify-content: center;
   align-items: center;
   min-height: 400px;
   padding: 20px;

   .license-card {
      max-width: 500px;
      width: 100%;
   }

   .license-message {
      text-align: center;
      padding: 20px;

      .license-icon {
         font-size: 48px;
         width: 48px;
         height: 48px;
         color: #ff9800;
         margin-bottom: 16px;
      }

      h2 {
         margin: 0 0 16px 0;
         color: #333;
      }

      p {
         margin: 0 0 12px 0;
         color: #666;
         line-height: 1.5;

         &:last-child {
            margin-bottom: 0;
         }
      }

      strong {
         color: #333;
      }
   }
}
