import {Component, inject} from '@angular/core';
import {LicenseService} from '../../../services/license.service';
import {MatDialog} from '@angular/material/dialog';
import {
   RestrictionsUpdateDialogComponent,
   RestrictionsUpdateDialogInput
} from '../../../dialogs/restrictions-update-dialog/restrictions-update-dialog.component';
import {NotificationService} from '../../../services/notification.service';

@Component({
   selector: 'app-channel-manager-settings',
   templateUrl: './channel-manager-settings.component.html',
   styleUrls: ['./channel-manager-settings.component.scss'],
   standalone: false
})
export class ChannelManagerSettingsComponent {
   protected hasChannelManagerLicense = inject(LicenseService).channelManager();

   private dialog = inject(MatDialog);
   private sNotification = inject(NotificationService);

   openRestrictionsUpdate(): void {
      const data: RestrictionsUpdateDialogInput = {};
      const dialog = this.dialog.open(RestrictionsUpdateDialogComponent, {
         data,
         width: '80vw',
         maxWidth: '1200px',
         height: '80vh'
      });

      dialog.afterClosed().subscribe(result => {
         if (result) {
            this.sNotification.displayNotification('Ограниченията са актуализирани успешно!');
         }
      });
   }
}
