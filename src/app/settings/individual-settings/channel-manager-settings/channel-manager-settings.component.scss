.settings-header {
   margin-bottom: 30px;

   .description {
      h1 {
         margin: 0 0 10px 0;
         color: #333;
      }

      h3 {
         margin: 0;
         font-weight: 400;
         color: #666;
      }
   }
}

.channel-manager-content {
   display: grid;
   grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
   gap: 20px;
   margin-top: 20px;

   .feature-card {
      mat-card-header {
         mat-card-title {
            display: flex;
            align-items: center;
            gap: 8px;

            mat-icon {
               color: #666;
            }
         }

         mat-card-subtitle {
            margin-top: 8px;
            color: #888;
         }
      }

      mat-card-content {
         p {
            margin: 0;
            color: #666;
            line-height: 1.5;
         }
      }

      mat-card-actions {
         padding-top: 16px;
      }
   }
}

.license-required {
   display: flex;
   justify-content: center;
   align-items: center;
   min-height: 400px;
   padding: 20px;

   .license-card {
      max-width: 500px;
      width: 100%;
   }

   .license-message {
      text-align: center;
      padding: 20px;

      .license-icon {
         font-size: 48px;
         width: 48px;
         height: 48px;
         color: #ff9800;
         margin-bottom: 16px;
      }

      h2 {
         margin: 0 0 16px 0;
         color: #333;
      }

      p {
         margin: 0 0 12px 0;
         color: #666;
         line-height: 1.5;

         &:last-child {
            margin-bottom: 0;
         }
      }

      strong {
         color: #333;
      }
   }
}
