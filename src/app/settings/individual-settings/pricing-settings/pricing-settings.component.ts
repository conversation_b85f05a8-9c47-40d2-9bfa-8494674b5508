import {Component, inject, OnInit} from '@angular/core';
import {PricingService} from '../../../services/pricing.service';
import {ID} from '../../../data/identifiable';
import {forkJoin, Observable} from 'rxjs';
import {
   cmpName,
   isDateInRange,
   moneyToString,
   rangesToString,
   today
} from '../../../utility/utility';
import {MatDialog} from '@angular/material/dialog';
import {filter, map, switchMap} from 'rxjs/operators';
import {Pricing} from '../../../data/pricing';
import {
   PricingDialogComponent,
   PricingDialogInput
} from '../../../dialogs/pricing-dialog/pricing-dialog.component';

import {Price} from '../../../data/price';
import {NotificationService} from '../../../services/notification.service';

interface PricingTableData {
   pricing: Pricing;
   tableData: {
      name: string;
      price: string;
   }[];
}

const getPriceNames = (id: ID, prices: Price[]) =>
   prices.find(b => b.id === id)?.name ?? '';

@Component({
   selector: 'app-pricing-settings',
   templateUrl: './pricing-settings.component.html',
   styles: [`
      .pricing-list:not(:nth-child(2)) {
         margin-top: 32px;
      }
   `],
   standalone: false
})
export class PricingSettingsComponent implements OnInit {
   pricingData$: Observable<PricingTableData[]> | undefined;

   private sPricing = inject(PricingService);
   private sNotification = inject(NotificationService);
   private dialog = inject(MatDialog);

   ngOnInit(): void {
      this.renewData();
   }

   addPricing(): void {
      this.openPricingDialog(false)
         .pipe(switchMap(p => this.sPricing.add(p)))
         .subscribe(() => this.renewData());
   }

   editPricing(pricing: Pricing): void {
      this.openPricingDialog(true, pricing)
         .pipe(switchMap(p => this.sPricing.update(p)))
         .subscribe(() => this.renewData());
   }

   deletePricing(pricing: Pricing): void {
      const activeRanges = pricing.ranges.map(r => r.dateRange)
         .filter(dr => isDateInRange(dr, today()));
      let activeRangesMessages: string[];
      if (activeRanges.length) {
         activeRangesMessages = [
            'Ценоразписът има следните активни периоди:',
            ...activeRanges.map(dr => rangesToString([dr]))
         ];
      } else {
         activeRangesMessages = ['Ценоразписът няма активни периоди!'];
      }

      this.sNotification.openConfirmationDialog({
         title: 'Изтриване на ценоразпис',
         description: [
            `Сигурни ли сте, че искате да изтриете "${pricing.name}"?`,
            ...activeRangesMessages
         ],
         yesText: 'Да, изтрий ценоразписът!',
      }).pipe(
         switchMap(() => this.sPricing.delete(pricing.id))
      ).subscribe(() => this.renewData());
   }

   private renewData(): void {
      this.pricingData$ = forkJoin([
         this.sPricing.getAll(),
         this.sPricing.getAllPrices()
      ]).pipe(map(([pricings, prices]) => pricings.map(pricing => ({
         pricing,
         tableData: Object.entries(pricing.bundlePrices)
            .map(([priceId, price]) => ({
               name: getPriceNames(priceId, prices),
               price: moneyToString(price),
            })).sort(cmpName)
      }))));
   }

   private openPricingDialog(edit: boolean, pricing?: Pricing): Observable<Pricing> {
      const data: PricingDialogInput = {edit, data: pricing};
      const dialog = this.dialog.open(PricingDialogComponent, {data});

      return dialog.afterClosed().pipe(filter(result => !!result));
   }
}
