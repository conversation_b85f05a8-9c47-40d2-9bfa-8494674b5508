import {NgModule} from '@angular/core';

import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {BrowserModule} from '@angular/platform-browser';
import {DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE} from '@angular/material/core';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {
   HTTP_INTERCEPTORS,
   provideHttpClient,
   withInterceptorsFromDi
} from '@angular/common/http';
import {MatPaginatorIntl} from '@angular/material/paginator';

import {
   ReservationDialogComponent
} from './dialogs/reservation-dialog/reservation-dialog.component';
import {AppComponent} from './app.component';
import {AppRoutingModule} from './app-routing.module';
import {CustomersComponent} from './customers/customers.component';
import {HomeComponent} from './home/<USER>';
import {MatPaginatorIntlBg} from './mat-paginator-intl-bg';
import {MaterialModule} from './material/material.module';
import {NavigationComponent} from './navigation/navigation.component';
import {NotFoundComponent} from './not-found/not-found.component';
import {QueriesComponent} from './query/queries/queries.component';
import {ReservationsComponent} from './reservations/reservations.component';
import {RoomsComponent} from './rooms/rooms.component';
// TODO: Remove this when Angular supports filtering in select
import {NgxMatSelectSearchModule} from 'ngx-mat-select-search';
import {
   CustomerDialogComponent
} from './dialogs/customer-dialog/customer-dialog.component';
import {
   ReservationFormComponent
} from './forms/reservation-form/reservation-form.component';
import {
   CustomerSelectComponent
} from './forms/inputs/customer-select/customer-select.component';
import {CustomerFormComponent} from './forms/customer-form/customer-form.component';
import {ViewContainerRefDirective} from './directives/view-container-ref.directive';
import {CalendarComponent} from './components/calendar/calendar.component';
import {
   ReservationContainerDirective
} from './directives/reservation-container.directive';
import {
   BundleSelectComponent
} from './forms/inputs/bundle-select/bundle-select.component';
import {CustomDateAdapter} from './custom-date-adapter';
import {TableQueryComponent} from './query/display/table-query/table-query.component';
import {FormatCustomerPipe} from './pipes/format-customer.pipe';
import {LoginFormComponent} from './forms/login-form/login-form.component';
import {LogoutComponent} from './auth/logout.component';
import {AuthInterceptor} from './auth/auth.interceptor';
import {OfferDialogComponent} from './dialogs/offer-dialog/offer-dialog.component';
import {OfferFormComponent} from './forms/offer-form/offer-form.component';
import {OffersComponent} from './offer/offers.component';
import {UnsubscribeComponent} from './utility/unsubscribe.component';
import {
   MAT_SNACK_BAR_DEFAULT_OPTIONS,
   MatSnackBarConfig
} from '@angular/material/snack-bar';
import {SettingsComponent} from './settings/settings.component';
import {
   BundlesSettingsComponent
} from './settings/individual-settings/bundles-settings/bundles-settings.component';
import {BundleFormComponent} from './forms/bundle-form/bundle-form.component';
import {
   ConsumableAmountsInputComponent
} from './forms/inputs/consumable-amounts-input/consumable-amounts-input.component';
import {
   ConfirmationDialogComponent
} from './dialogs/confirmation-dialog/confirmation-dialog.component';
import {
   CheckInDialogComponent
} from './dialogs/check-in-dialog/check-in-dialog.component';
import {
   CustomerListInputComponent
} from './forms/inputs/customer-list-input/customer-list-input.component';
import {
   CheckOutDialogComponent
} from './dialogs/check-out-dialog/check-out-dialog.component';
import {
   CustomerPickerDialogComponent
} from './dialogs/customer-picker-dialog/customer-picker-dialog.component';
import {
   PricingSettingsComponent
} from './settings/individual-settings/pricing-settings/pricing-settings.component';
import {MoneyPipe} from './pipes/money.pipe';
import {DateRangesPipe} from './pipes/date-ranges.pipe';
import {
   RoomsSettingsComponent
} from './settings/individual-settings/rooms-settings/rooms-settings.component';
import {RoomFormComponent} from './forms/room-form/room-form.component';
import {
   DiscountsSettingsComponent
} from './settings/individual-settings/discounts-settings/discounts-settings.component';
import {CustomerGroupPipe} from './pipes/customer-group.pipe';
import {
   CustomerGroupFormComponent
} from './forms/customer-group-form/customer-group-form.component';
import {
   CustomerDiscountFormComponent
} from './forms/customer-discount-form/customer-discount-form.component';
import {PricingDialogComponent} from './dialogs/pricing-dialog/pricing-dialog.component';
import {PricingFormComponent} from './forms/pricing-form/pricing-form.component';
import {MoneyInputComponent} from './forms/inputs/money-input/money-input.component';
import {InfoIconComponent} from './components/info-icon.component';
import {
   DiscountInputComponent
} from './forms/inputs/discount-input/discount-input.component';
import {DialogComponent} from './dialogs/dialog/dialog.component';
import {BalanceDialogComponent} from './dialogs/balance-dialog/balance-dialog.component';
import {
   PurchaseDialogComponent
} from './dialogs/purchase-dialog/purchase-dialog.component';
import {PaymentDialogComponent} from './dialogs/payment-dialog/payment-dialog.component';
import {PurchaseFormComponent} from './forms/purchase-form/purchase-form.component';
import {PaymentFormComponent} from './forms/payment-form/payment-form.component';
import {FormDialogComponent} from './dialogs/form-dialog/form-dialog.component';
import {
   BusinessSettingsComponent
} from './settings/individual-settings/business-settings/business-settings.component';
import {
   GuestGroupCountInputComponent
} from './forms/inputs/guest-group-count-input/guest-group-count-input.component';
import {VATPipe} from './pipes/vat.pipe';
import {ReservationDurationPipe} from './pipes/reservation-duration.pipe';
import {ReservationPeoplePipe} from './pipes/reservation-people.pipe';
import {
   PeriodicTableQueryComponent
} from './query/display/periodic-table-query/periodic-table-query.component';
import {DateTimePipe} from './pipes/date-time.pipe';
import {CleaningComponent} from './cleaning/cleaning.component';
import {
   ReservationSelectDialogComponent
} from './dialogs/reservation-select-dialog/reservation-select-dialog.component';
import {
   ReservationSearchComponent
} from './forms/inputs/reservation-search/reservation-search.component';
import {ReservationPipe} from './pipes/reservation.pipe';
import {ColorInputComponent} from './forms/inputs/color-input/color-input.component';
import {BalanceTableComponent} from './components/balance-table/balance-table.component';
import {ErrorInterceptor} from './services/error.interceptor';
import {EventDirective} from './directives/event.directive';
import {environment} from '../environments/environment';
import {OperatorFormComponent} from './forms/operator-form/operator-form.component';
import {
   OperatorsSettingsComponent
} from './settings/individual-settings/operators-settings/operators-settings.component';
import {
   GroupedTableQueryComponent
} from './query/display/grouped-table-query/grouped-table-query.component';
import {RefreshTokenInterceptor} from './auth/refresh-token.interceptor';
import {
   CleaningSettingsComponent
} from './settings/individual-settings/cleaning-settings/cleaning-settings.component';
import {
   SourcesSettingsComponent
} from './settings/individual-settings/sources-settings/sources-settings.component';
import {
   GroupReservationDialogComponent
} from './dialogs/group-reservation-dialog/group-reservation-dialog.component';
import {
   GroupReservationFormComponent
} from './forms/group-reservation-form/group-reservation-form.component';
import {
   ReservationSourceInputComponent
} from './forms/inputs/reservation-source-input/reservation-source-input.component';
import {BankFormComponent} from './forms/bank-form/bank-form.component';
import {
   AccommodationPlaceFormComponent
} from './forms/accommodation-place/accommodation-place-form.component';
import {
   AssignCleaningDialogComponent
} from './dialogs/assign-cleaning-dialog/assign-cleaning-dialog.component';
import {
   SequenceInputComponent
} from './forms/inputs/sequence-input/sequence-input.component';
import {
   PricingReferenceComponent
} from './components/prices/pricing-reference/pricing-reference.component';
import {WeekDaysPipe} from './week-days.pipe';
import {FinancialAccountPipe} from './pipes/financial-account.pipe';
import {
   ReservationOrFinancialAccountPipe
} from './pipes/reservation-or-financial-account.pipe';
import {VoucherDialogComponent} from './dialogs/voucher-dialog/voucher-dialog.component';
import {
   VoucherInputComponent
} from './forms/inputs/voucher-input/voucher-input.component';
import {PaymentMethodPipe} from './pipes/payment-method.pipe';
import {
   ReservationHistoryDialogComponent
} from './dialogs/reservation-history-dialog/reservation-history-dialog.component';
import {
   CustomerGroupSelectComponent
} from './components/customer/customer-group-select/customer-group-select.component';
import {
   ConsumptionRuleFormComponent
} from './forms/consumption-rule-form/consumption-rule-form.component';
import {
   PriceSelectComponent
} from './components/prices/price-select/price-select.component';
import {
   AccommodationConditionInputComponent
} from './forms/inputs/accommodation-condition-input/accommodation-condition-input.component';
import {
   VoucherSourceFormComponent
} from './forms/voucher-source-form/voucher-source-form.component';
import {
   MessagesSettingsComponent
} from './settings/individual-settings/messages-settings/messages-settings.component';
import {
   NgxGoogleAnalyticsModule,
   NgxGoogleAnalyticsRouterModule
} from 'ngx-google-analytics';
import {MAT_TOOLTIP_DEFAULT_OPTIONS} from '@angular/material/tooltip';
import {MAT_FORM_FIELD_DEFAULT_OPTIONS} from '@angular/material/form-field';
import {DataTableComponent} from './settings/data-table/data-table.component';
import {PropertyPipe} from './pipes/property.pipe';
import {VoucherPipe} from './pipes/voucher.pipe';
import {RoomSwapComponent} from './components/calendar/room-swap.component';
import {CommaJoinPipe} from './pipes/comma-join.pipe';
import {AccommodationConditionTypePipe} from './pipes/accommodation-condition-type.pipe';
import {
   MultipleQueriesComponent
} from './query/display/multiple-queries/multiple-queries.component';
import {NotesComponent} from './components/notes/notes.component';
import {NotesPipe} from './pipes/notes.pipe';
import {FiscalButtonComponent} from './components/fiscal-button/fiscal-button.component';
import {AmountDialogComponent} from './dialogs/amount-dialog.component';
import {
   FiscalSettingsComponent
} from './settings/individual-settings/fiscal-settings/fiscal-settings.component';
import {
   ChannelManagerSettingsComponent
} from './settings/individual-settings/channel-manager-settings/channel-manager-settings.component';
import {
   PaymentReversalDialogComponent
} from './dialogs/payment-reversal-dialog/payment-reversal-dialog.component';
import {
   IdScannerIconComponent
} from './components/id-scanner-icon/id-scanner-icon.component';
import {
   IdScannerResultDialogComponent
} from './dialogs/id-scanner-result-dialog.component';
import {CustomerDocumentPipe} from './pipes/customer-document.pipe';
import {CustomerGenderPipe} from './pipes/customer-gender.pipe';
import {StarRatingComponent} from './components/star-rating.component';
import {CancellationsComponent} from './cancellations/cancellations.component';
import {
   InvoiceButtonComponent
} from './components/invoice-button/invoice-button.component';
import {InvoicesComponent} from './invoices/invoices.component';
import {
   CustomerAccountsDialogComponent
} from './dialogs/customer-accounts-dialog/customer-accounts-dialog.component';
import {
   RestrictionsUpdateFormComponent
} from './forms/restrictions-update-form/restrictions-update-form.component';
import {
   RestrictionsUpdateDialogComponent
} from './dialogs/restrictions-update-dialog/restrictions-update-dialog.component';

const snackbarConfig: MatSnackBarConfig = {
   duration: 2000,
   horizontalPosition: 'start',
   verticalPosition: 'bottom'
};

const trackingModule = environment.gAnalytics.enabled ? [
   NgxGoogleAnalyticsModule.forRoot(environment.gAnalytics.measurementId),
   NgxGoogleAnalyticsRouterModule
] : [];

@NgModule({
   declarations: [
      AppComponent,
      HomeComponent,
      NavigationComponent,
      ReservationsComponent,
      CustomersComponent,
      QueriesComponent,
      NotFoundComponent,
      ReservationDialogComponent,
      RoomsComponent,
      CustomerDialogComponent,
      ReservationFormComponent,
      CustomerSelectComponent,
      CustomerFormComponent,
      ViewContainerRefDirective,
      CalendarComponent,
      ReservationContainerDirective,
      BundleSelectComponent,
      ConsumableAmountsInputComponent,
      TableQueryComponent,
      FormatCustomerPipe,
      LoginFormComponent,
      LogoutComponent,
      OfferDialogComponent,
      OfferFormComponent,
      OffersComponent,
      UnsubscribeComponent,
      SettingsComponent,
      BundlesSettingsComponent,
      BundleFormComponent,
      ConfirmationDialogComponent,
      CheckInDialogComponent,
      CustomerListInputComponent,
      CheckOutDialogComponent,
      CustomerPickerDialogComponent,
      PricingSettingsComponent,
      MoneyPipe,
      DateRangesPipe,
      RoomsSettingsComponent,
      RoomFormComponent,
      DiscountsSettingsComponent,
      CustomerGroupPipe,
      CustomerGroupFormComponent,
      CustomerDiscountFormComponent,
      PricingDialogComponent,
      PricingFormComponent,
      MoneyInputComponent,
      InfoIconComponent,
      DiscountInputComponent,
      DialogComponent,
      BalanceDialogComponent,
      PurchaseDialogComponent,
      PaymentDialogComponent,
      PurchaseFormComponent,
      PaymentFormComponent,
      FormDialogComponent,
      BusinessSettingsComponent,
      GuestGroupCountInputComponent,
      VATPipe,
      ReservationDurationPipe,
      ReservationPeoplePipe,
      PeriodicTableQueryComponent,
      DateTimePipe,
      CleaningComponent,
      ReservationSelectDialogComponent,
      ReservationSearchComponent,
      ReservationPipe,
      ColorInputComponent,
      BalanceTableComponent,
      EventDirective,
      OperatorFormComponent,
      OperatorsSettingsComponent,
      GroupedTableQueryComponent,
      CleaningSettingsComponent,
      SourcesSettingsComponent,
      GroupReservationDialogComponent,
      GroupReservationFormComponent,
      ReservationSourceInputComponent,
      BankFormComponent,
      AccommodationPlaceFormComponent,
      AssignCleaningDialogComponent,
      SequenceInputComponent,
      PricingReferenceComponent,
      WeekDaysPipe,
      FinancialAccountPipe,
      ReservationOrFinancialAccountPipe,
      VoucherDialogComponent,
      VoucherInputComponent,
      PaymentMethodPipe,
      ReservationHistoryDialogComponent,
      CustomerGroupSelectComponent,
      ConsumptionRuleFormComponent,
      PriceSelectComponent,
      AccommodationConditionInputComponent,
      VoucherSourceFormComponent,
      MessagesSettingsComponent,
      DataTableComponent,
      RoomSwapComponent,
      AccommodationConditionTypePipe,
      MultipleQueriesComponent,
      NotesComponent,
      NotesPipe,
      FiscalButtonComponent,
      AmountDialogComponent,
      FiscalSettingsComponent,
      ChannelManagerSettingsComponent,
      PaymentReversalDialogComponent,
      IdScannerIconComponent,
      IdScannerResultDialogComponent,
      CustomerDocumentPipe,
      CustomerGenderPipe,
      CancellationsComponent,
      InvoiceButtonComponent,
      InvoicesComponent,
      CustomerAccountsDialogComponent,
      RestrictionsUpdateFormComponent,
      RestrictionsUpdateDialogComponent,
   ],
   imports: [
      AppRoutingModule,
      BrowserAnimationsModule,
      BrowserModule,
      FormsModule,
      MaterialModule,
      ReactiveFormsModule,
      NgxMatSelectSearchModule,
      ...trackingModule,
      PropertyPipe,
      VoucherPipe,
      CommaJoinPipe,
      StarRatingComponent,
   ],
   providers: [
      {provide: MAT_DATE_LOCALE, useValue: 'bg-BG'},
      {provide: DateAdapter, useClass: CustomDateAdapter, deps: [MAT_DATE_LOCALE]},
      {
         provide: MAT_DATE_FORMATS, useValue: {
            parse: {dateInput: 'd.L.yy'},
            display: {
               dateA11yLabel: 'DD',
               dateInput: 'd.L.yy',
               monthYearA11yLabel: 'LLLL yyyy',
               monthYearLabel: 'LLL yyyy',
            }
         }
      },
      {provide: MatPaginatorIntl, useClass: MatPaginatorIntlBg},
      {provide: HTTP_INTERCEPTORS, useClass: RefreshTokenInterceptor, multi: true},
      {provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true},
      {provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true},
      {provide: MAT_SNACK_BAR_DEFAULT_OPTIONS, useValue: snackbarConfig},
      {provide: MAT_TOOLTIP_DEFAULT_OPTIONS, useValue: {showDelay: 500}},
      {
         provide: MAT_FORM_FIELD_DEFAULT_OPTIONS, useValue: {
            subscriptSizing: 'dynamic',
            appearance: 'outline',
         }
      },
      provideHttpClient(withInterceptorsFromDi()),
   ],
   bootstrap: [AppComponent]
})
export class AppModule {
}
