import {Pipe, PipeTransform} from '@angular/core';
import {Reservation, ReservationInfo} from '../data/reservation';
import {dateFmt} from '../utility/utility';
import {fullName} from '../data/customers/customer';

@Pipe({
   name: 'reservation',
   standalone: false
})
export class ReservationPipe implements PipeTransform {
   static toString(reservation?: Reservation | ReservationInfo) {
      if (!reservation) {
         return '';
      }

      const {serialNumber, titular, room, start, end} = reservation;
      let result = `(${serialNumber}) ${fullName(titular)}  от ${dateFmt(
         start)} до ${dateFmt(end)}`;
      if (room) {
         result = `${result} в стая ${room.name}`;
      }

      return result;
   }

   transform(reservation?: Reservation | ReservationInfo): string {
      return ReservationPipe.toString(reservation);
   }
}
