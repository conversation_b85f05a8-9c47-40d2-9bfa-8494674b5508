import {inject, Injectable} from '@angular/core';
import {Pricing, RestrictionsUpdate} from '../data/pricing';
import {Observable, of} from 'rxjs';
import {Price} from '../data/price';
import {MaybeID} from '../data/identifiable';
import {DtoCache} from '../utility/dto-cache';
import {HttpClient} from '@angular/common/http';
import {ConsumableService} from './consumable.service';
import {serverUrl} from '../utility/http-utility';

@Injectable({
   providedIn: 'root'
})
export class PricingService extends DtoCache<Pricing, Pricing> {
   private sConsumable = inject(ConsumableService);

   constructor(http: HttpClient) {
      super('pricing', http);
   }

   getAllPrices(): Observable<Price[]> {
      return this.sConsumable.getAll();
   }

   submitRestrictionsUpdate(update: RestrictionsUpdate): Observable<void> {
      return this.http.post<void>(serverUrl("channel-manager/restrictions-update"), update);
   }

   protected mapToLocal(value: Pricing): Observable<Pricing> {
      console.log(typeof (value.ranges[0]?.dateRange.start))
      return of(value);
   }

   protected mapToRemote(value: MaybeID<Pricing>): MaybeID<Pricing> {
      return value;
   }
}
