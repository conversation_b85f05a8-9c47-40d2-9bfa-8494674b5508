import {inject, Injectable, signal} from '@angular/core';
import {Observable} from 'rxjs';
import {HttpClient} from '@angular/common/http';
import {serverUrl} from '../utility/http-utility';
import {map} from 'rxjs/operators';

export enum LicenseType {
   idScanner = 'ID_SCANNER',
   guestApp = 'GUEST_APP',
   channelManager = 'CHANNEL_MANAGER'
}

@Injectable({providedIn: 'root'})
export class LicenseService {
   idScanner = signal(false);
   guestApp = signal(false);
   channelManager = signal(false);

   private http = inject(HttpClient);
   private url = serverUrl('license');

   init(): Observable<void> {
      return this.http.get<LicenseType[]>(this.url).pipe(map(ls => {
         this.idScanner.set(ls.includes(LicenseType.idScanner));
         this.guestApp.set(ls.includes(LicenseType.guestApp));
         this.channelManager.set(ls.includes(LicenseType.channelManager));
      }));
   }

   getAll(): Observable<LicenseType[]> {
      return this.http.get<LicenseType[]>(this.url);
   }
}
