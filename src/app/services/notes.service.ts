import {inject, Injectable} from '@angular/core';

import {forkJoin, Observable, of, switchMap} from 'rxjs';
import {ID, Identifiable, MaybeID, NoID} from 'src/app/data/identifiable';
import {BatchNote, Note} from 'src/app/data/notes';
import {DateTime} from 'luxon';
import {catchError, defaultIfEmpty, map} from 'rxjs/operators';
import {OperatorService} from './operator.service';
import {addId, serverUrl} from '../utility/http-utility';
import {HttpClient} from '@angular/common/http';

interface NoteDTO extends Identifiable {
   content: string;
   parent: ID;
   created?: string;
   creator?: ID;
}

@Injectable({
   providedIn: 'root'
})
export class NotesService {
   private baseUrl = serverUrl('note');
   private idUrl = addId.bind(null, this.baseUrl);

   private http = inject(HttpClient);
   private sOperator = inject(OperatorService);

   getByParent(parentId: ID): Observable<Note[]> {
      return this.http.get<NoteDTO[]>(this.baseUrl, {params: {parentId}}).pipe(
         switchMap(notes => forkJoin(notes.map(note => this.mapToLocal(note)))),
         defaultIfEmpty([]),
      );
   }

   batchAdd(batchNote: BatchNote): Observable<Note[]> {
      return this.http.post<ID[]>(`${this.baseUrl}/batch-create`, batchNote).pipe(
         switchMap(noteIds => forkJoin(noteIds.map(id => this.get(id))))
      );
   }

   moveToNewParent(oldParent: ID, newParent: ID): Observable<void> {
      const body = {oldParent, newParent};
      return this.http.post<void>(`${this.baseUrl}/switch-parent`, body);
   }

   get(id: ID): Observable<Note> {
      return this.http.get<NoteDTO>(this.idUrl(id)).pipe(
         switchMap(note => this.mapToLocal(note))
      );
   }

   add(newItem: NoID<Note>): Observable<Note> {
      return this.http.post<ID>(this.baseUrl, this.mapToRemote(newItem)).pipe(
         switchMap(newId => this.get(newId))
      );
   }

   delete(noteId: ID): Observable<void> {
      return this.http.delete<void>(this.idUrl(noteId));
   }

   private mapToLocal(note: NoteDTO): Observable<Note> {
      const {creator, created, ...rest} = note;
      const createdDateTime = DateTime.fromMillis(parseInt(created!));
      if (creator) {
         return this.sOperator.get(creator).pipe(map(operator => ({
            ...rest,
            creator: operator,
            created: createdDateTime
         })),
            catchError(() => of({
               ...rest,
               created: createdDateTime
            })));
      } else {
         return of({
            ...rest,
            created: createdDateTime
         });
      }

   }

   private mapToRemote(note: any): MaybeID<NoteDTO> {
      return {
         id: note.id,
         content: note.content,
         parent: note.parent
      };
   }
}
